// Test Socket.IO connection
import socket from './socket';

console.log('Testing Socket.IO connection...');

socket.on('connect', () => {
  console.log('✅ Socket.IO connected successfully!', socket.id);
  
  // Test joining a poll room
  socket.emit('join-poll', 'test-poll-id');
  console.log('📡 Emitted join-poll event');
  
  // Test voting
  socket.emit('vote', {
    pollId: 'test-poll-id',
    optionIndex: 0,
    socketId: socket.id
  });
  console.log('📡 Emitted vote event');
});

socket.on('disconnect', () => {
  console.log('❌ Socket.IO disconnected');
});

socket.on('connect_error', (error) => {
  console.error('❌ Socket.IO connection error:', error);
});

socket.on('error', (data) => {
  console.error('❌ Socket.IO error:', data);
});

socket.on('vote-success', (data) => {
  console.log('✅ Vote success:', data);
});

socket.on('poll-update', (data) => {
  console.log('📊 Poll update received:', data);
});

export default socket;
