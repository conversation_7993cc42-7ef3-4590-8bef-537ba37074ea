import React, { useState, useEffect } from 'react';
import api from '../api';
import socket from '../socket';

function TestPage() {
  const [connectionStatus, setConnectionStatus] = useState('Checking...');
  const [apiStatus, setApiStatus] = useState('Checking...');
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, status, details) => {
    setTestResults(prev => [...prev, { test, status, details, timestamp: new Date() }]);
  };

  useEffect(() => {
    // Test Socket.IO connection
    if (socket.connected) {
      setConnectionStatus('✅ Connected');
      addTestResult('Socket.IO Connection', 'success', `Connected with ID: ${socket.id}`);
    } else {
      setConnectionStatus('❌ Disconnected');
      addTestResult('Socket.IO Connection', 'error', 'Not connected');
    }

    socket.on('connect', () => {
      setConnectionStatus('✅ Connected');
      addTestResult('Socket.IO Connection', 'success', `Connected with ID: ${socket.id}`);
    });

    socket.on('disconnect', () => {
      setConnectionStatus('❌ Disconnected');
      addTestResult('Socket.IO Connection', 'error', 'Disconnected');
    });

    // Test API connection
    const testAPI = async () => {
      try {
        const response = await api.get('/health');
        setApiStatus('✅ API Working');
        addTestResult('API Health Check', 'success', 'Backend API is responding');
      } catch (error) {
        setApiStatus('❌ API Error');
        addTestResult('API Health Check', 'error', error.message);
      }
    };

    testAPI();

    return () => {
      socket.off('connect');
      socket.off('disconnect');
    };
  }, []);

  const testCreatePoll = async () => {
    try {
      const response = await api.post('/polls', {
        question: 'Test Poll - Is everything working?',
        options: ['Yes', 'No', 'Partially']
      });
      addTestResult('Create Poll', 'success', `Poll created with ID: ${response.data._id}`);
      return response.data;
    } catch (error) {
      addTestResult('Create Poll', 'error', error.message);
    }
  };

  const testSocketVote = (pollId) => {
    if (!socket.connected) {
      addTestResult('Socket Vote', 'error', 'Socket not connected');
      return;
    }

    socket.emit('vote', {
      pollId: pollId,
      optionIndex: 0,
      socketId: socket.id
    });
    addTestResult('Socket Vote', 'info', 'Vote emitted via socket');

    // Listen for response
    socket.once('vote-success', (data) => {
      addTestResult('Socket Vote Response', 'success', 'Vote success received');
    });

    socket.once('error', (data) => {
      addTestResult('Socket Vote Response', 'error', data.message);
    });
  };

  const runFullTest = async () => {
    addTestResult('Full Test', 'info', 'Starting comprehensive test...');
    
    // Test poll creation
    const poll = await testCreatePoll();
    if (poll) {
      // Test socket voting
      setTimeout(() => {
        testSocketVote(poll._id);
      }, 1000);
    }
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h1>🧪 Live Poll & Quiz App - Test Page</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>Connection Status</h2>
        <p><strong>Socket.IO:</strong> {connectionStatus}</p>
        <p><strong>API:</strong> {apiStatus}</p>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Quick Tests</h2>
        <button onClick={testCreatePoll} style={{ marginRight: '1rem', padding: '0.5rem 1rem' }}>
          Test Create Poll
        </button>
        <button onClick={runFullTest} style={{ padding: '0.5rem 1rem' }}>
          Run Full Test
        </button>
      </div>

      <div>
        <h2>Test Results</h2>
        <div style={{ maxHeight: '400px', overflowY: 'auto', border: '1px solid #ccc', padding: '1rem' }}>
          {testResults.length === 0 ? (
            <p>No tests run yet...</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index} style={{ 
                marginBottom: '1rem', 
                padding: '0.5rem', 
                backgroundColor: result.status === 'success' ? '#d4edda' : 
                                result.status === 'error' ? '#f8d7da' : '#d1ecf1',
                borderRadius: '4px'
              }}>
                <strong>{result.test}</strong> - {result.status}
                <br />
                <small>{result.details}</small>
                <br />
                <small style={{ color: '#666' }}>
                  {result.timestamp.toLocaleTimeString()}
                </small>
              </div>
            ))
          )}
        </div>
      </div>

      <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>Instructions for Testing</h3>
        <ol>
          <li>Check that Socket.IO shows "Connected" status</li>
          <li>Check that API shows "Working" status</li>
          <li>Click "Test Create Poll" to verify backend functionality</li>
          <li>Click "Run Full Test" to test the complete flow</li>
          <li>Open browser console (F12) to see detailed logs</li>
          <li>Try creating a poll from the Admin Dashboard</li>
          <li>Open the poll URL in a new tab and try voting</li>
        </ol>
      </div>
    </div>
  );
}

export default TestPage;
