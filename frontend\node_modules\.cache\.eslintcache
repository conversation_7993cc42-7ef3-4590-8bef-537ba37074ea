[{"C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\QuizSession.js": "3", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\PollSession.js": "4", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\components\\Header.js": "5", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\ParticipantDashboard.js": "6", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\AdminDashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\ControlCenter.js": "8", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\socket.js": "9", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\api.js": "10", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\mockApi.js": "11", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\components\\StatusDisplay.js": "12", "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\TestPage.js": "13"}, {"size": 255, "mtime": 1748801661624, "results": "14", "hashOfConfig": "15"}, {"size": 1161, "mtime": 1748841812687, "results": "16", "hashOfConfig": "15"}, {"size": 7616, "mtime": 1748837272999, "results": "17", "hashOfConfig": "15"}, {"size": 6102, "mtime": 1748841769267, "results": "18", "hashOfConfig": "15"}, {"size": 970, "mtime": 1748801691550, "results": "19", "hashOfConfig": "15"}, {"size": 3477, "mtime": 1748837240119, "results": "20", "hashOfConfig": "15"}, {"size": 7913, "mtime": 1748837222942, "results": "21", "hashOfConfig": "15"}, {"size": 8850, "mtime": 1748837300343, "results": "22", "hashOfConfig": "15"}, {"size": 574, "mtime": 1748841746507, "results": "23", "hashOfConfig": "15"}, {"size": 3305, "mtime": 1748841473533, "results": "24", "hashOfConfig": "15"}, {"size": 5275, "mtime": 1748838805659, "results": "25", "hashOfConfig": "15"}, {"size": 3046, "mtime": 1748839166574, "results": "26", "hashOfConfig": "15"}, {"size": 5407, "mtime": 1748841794498, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1werx11", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\QuizSession.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\PollSession.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\ParticipantDashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\ControlCenter.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\socket.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\api.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\mockApi.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\components\\StatusDisplay.js", [], [], "C:\\Users\\<USER>\\Downloads\\lpqa cpy\\frontend\\src\\pages\\TestPage.js", ["67"], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 37, "column": 15, "nodeType": "70", "messageId": "71", "endLine": 37, "endColumn": 23}, "no-unused-vars", "'response' is assigned a value but never used.", "Identifier", "unusedVar"]