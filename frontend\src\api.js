import axios from 'axios';
import mockApi from './mockApi';

// Create axios instance with base URL
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? '/api'
    : 'http://localhost:5000/api',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Check if backend is available
let useBackend = true;

// Test backend availability
const testBackend = async () => {
  try {
    await api.get('/health');
    console.log('✅ Backend is available');
    useBackend = true;
  } catch (error) {
    console.log('⚠️ Backend not available, using mock API');
    useBackend = false;
  }
};

// Test backend on startup with retry (limited)
const initializeApi = async () => {
  await testBackend();
  if (!useBackend) {
    // Retry after 2 seconds, but only once
    setTimeout(async () => {
      await testBackend();
    }, 2000);
  }
};

// Only initialize once
if (!window.apiInitialized) {
  window.apiInitialized = true;
  initializeApi();
}

// Enhanced API wrapper with fallback to mock
const enhancedApi = {
  async get(url) {
    if (!useBackend) {
      // Handle mock API calls
      if (url === '/polls') return mockApi.getPolls();
      if (url === '/quizzes') return mockApi.getQuizzes();
      if (url.startsWith('/polls/')) {
        const id = url.split('/')[2];
        return mockApi.getPoll(id);
      }
      if (url.startsWith('/quizzes/')) {
        const id = url.split('/')[2];
        return mockApi.getQuiz(id);
      }
      if (url.startsWith('/sessions/')) {
        const code = url.split('/')[2];
        return mockApi.getSession(code);
      }
    }

    try {
      const response = await api.get(url);
      console.log('API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('API Error:', error.message, 'Falling back to mock API');
      useBackend = false;
      return this.get(url); // Retry with mock
    }
  },

  async post(url, data) {
    if (!useBackend) {
      // Handle mock API calls
      if (url === '/polls') return mockApi.createPoll(data);
      if (url === '/quizzes') return mockApi.createQuiz(data);
      if (url.includes('/vote')) {
        const pollId = url.split('/')[1];
        return mockApi.vote(pollId, data);
      }
      if (url.includes('/answer')) {
        const quizId = url.split('/')[1];
        return mockApi.answer(quizId, data);
      }
    }

    try {
      console.log('API Request:', 'POST', url, data);
      const response = await api.post(url, data);
      console.log('API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('API Error:', error.message, 'Falling back to mock API');
      useBackend = false;
      return this.post(url, data); // Retry with mock
    }
  },

  async patch(url, data) {
    if (!useBackend) {
      console.log('Mock API: PATCH not implemented for', url);
      return { data: { success: true } };
    }

    try {
      const response = await api.patch(url, data);
      console.log('API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('API Error:', error.message);
      useBackend = false;
      return { data: { success: true } };
    }
  },

  async delete(url) {
    if (!useBackend) {
      console.log('Mock API: DELETE not implemented for', url);
      return { data: { success: true } };
    }

    try {
      const response = await api.delete(url);
      console.log('API Response:', response.status, url);
      return response;
    } catch (error) {
      console.error('API Error:', error.message);
      useBackend = false;
      return { data: { success: true } };
    }
  }
};

export default enhancedApi;
