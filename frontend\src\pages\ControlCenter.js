import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../api';
import socket from '../socket';
// QR Code removed as requested

function ControlCenter() {
  const [polls, setPolls] = useState([]);
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  // selectedSession removed with QR code functionality
  const navigate = useNavigate();

  useEffect(() => {
    fetchSessions();
    
    // Listen for real-time updates
    socket.on('poll-update', (data) => {
      setPolls(prev => prev.map(poll => 
        poll._id === data.poll._id ? data.poll : poll
      ));
    });

    socket.on('quiz-update', (data) => {
      setQuizzes(prev => prev.map(quiz => 
        quiz._id === data.quiz._id ? data.quiz : quiz
      ));
    });

    socket.on('results-visibility-changed', (data) => {
      if (data.poll) {
        setPolls(prev => prev.map(poll => 
          poll._id === data.poll._id ? data.poll : poll
        ));
      }
      if (data.quiz) {
        setQuizzes(prev => prev.map(quiz => 
          quiz._id === data.quiz._id ? data.quiz : quiz
        ));
      }
    });

    return () => {
      socket.off('poll-update');
      socket.off('quiz-update');
      socket.off('results-visibility-changed');
    };
  }, []);

  const fetchSessions = async () => {
    try {
      const [pollsResponse, quizzesResponse] = await Promise.all([
        api.get('/polls'),
        api.get('/quizzes')
      ]);
      
      setPolls(pollsResponse.data);
      setQuizzes(quizzesResponse.data);
    } catch (err) {
      setError('Failed to fetch sessions');
    } finally {
      setLoading(false);
    }
  };

  const toggleResults = async (type, id) => {
    try {
      console.log(`🔄 Toggling results for ${type} ${id}`);
      const response = await api.patch(`/${type}s/${id}/toggle-results`);
      console.log('✅ Toggle results response:', response.data);

      // Update local state immediately
      if (type === 'poll') {
        setPolls(prev => prev.map(poll =>
          poll._id === id ? { ...poll, showResults: response.data.showResults } : poll
        ));
      } else {
        setQuizzes(prev => prev.map(quiz =>
          quiz._id === id ? { ...quiz, showResults: response.data.showResults } : quiz
        ));
      }

      // Emit socket event for real-time updates
      socket.emit('toggle-results', { type, id });
    } catch (err) {
      console.error('❌ Toggle results error:', err);
      setError(`Failed to toggle results for ${type}`);
    }
  };

  const endSession = async (type, id) => {
    if (window.confirm(`Are you sure you want to end this ${type}?`)) {
      try {
        await api.patch(`/${type}s/${id}/end`);
        fetchSessions(); // Refresh the list
      } catch (err) {
        setError(`Failed to end ${type}`);
      }
    }
  };

  const deleteSession = async (type, id) => {
    if (window.confirm(`Are you sure you want to permanently delete this ${type}? This action cannot be undone.`)) {
      try {
        await api.delete(`/${type}s/${id}`);
        fetchSessions(); // Refresh the list
        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`);
      } catch (err) {
        setError(`Failed to delete ${type}`);
      }
    }
  };

  const joinAsParticipant = (type, id) => {
    const url = `/${type}/${id}`;
    window.open(url, '_blank');
  };

  const getTotalVotes = (session, type) => {
    if (type === 'poll') {
      return session.options.reduce((total, option) => total + option.voteCount, 0);
    } else {
      return session.options.reduce((total, option) => total + option.selectedCount, 0);
    }
  };

  const getCorrectAnswers = (quiz) => {
    return quiz.options[quiz.correctAnswer]?.selectedCount || 0;
  };

  const getSuccessRate = (quiz) => {
    const total = getTotalVotes(quiz, 'quiz');
    const correct = getCorrectAnswers(quiz);
    return total > 0 ? Math.round((correct / total) * 100) : 0;
  };

  const SessionCard = ({ session, type }) => (
    <div className="session-card">
      <div className="session-header">
        <div className="session-type-badge">
          {type === 'poll' ? '📊' : '🧠'} {type.toUpperCase()}
        </div>
        <div className={`session-status ${session.isActive ? 'active' : 'ended'}`}>
          {session.isActive ? 'Active' : 'Ended'}
        </div>
      </div>

      <div className="session-content">
        <h3 className="session-question">{session.question}</h3>
        <div className="session-code-info">
          <span className="session-code-label">Session Code:</span>
          <span className="session-code-value">{session.sessionCode}</span>
        </div>
        <div className="session-stats">
          <div className="stat">
            <span className="stat-value">{getTotalVotes(session, type)}</span>
            <span className="stat-label">{type === 'poll' ? 'Votes' : 'Answers'}</span>
          </div>
          <div className="stat">
            <span className="stat-value">{session.participants?.length || 0}</span>
            <span className="stat-label">Participants</span>
          </div>
          {type === 'quiz' && (
            <div className="stat">
              <span className="stat-value">{getSuccessRate(session)}%</span>
              <span className="stat-label">Success Rate</span>
            </div>
          )}
        </div>

        <div className="session-options">
          {session.options.map((option, index) => {
            const count = type === 'poll' ? option.voteCount : option.selectedCount;
            const total = getTotalVotes(session, type);
            const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
            
            return (
              <div key={index} className="option-result">
                <div className="option-header">
                  <span className="option-text">
                    {String.fromCharCode(65 + index)}. {option.text}
                    {type === 'quiz' && index === session.correctAnswer && ' ✅'}
                  </span>
                  <span className="option-count">{count}</span>
                </div>
                <div className="option-bar">
                  <div 
                    className={`option-fill ${type === 'quiz' && index === session.correctAnswer ? 'correct' : ''}`}
                    style={{ width: `${percentage}%` }}
                  >
                    {percentage}%
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="session-actions">
        <button 
          className="btn btn-primary btn-sm"
          onClick={() => joinAsParticipant(type, session._id)}
        >
          Join as Participant
        </button>
        
        {session.isActive && (
          <button 
            className={`btn btn-sm ${session.showResults ? 'btn-warning' : 'btn-success'}`}
            onClick={() => toggleResults(type, session._id)}
          >
            {session.showResults ? 'Hide Results' : 'Show Results'}
          </button>
        )}
        
        {session.isActive && (
          <button 
            className="btn btn-danger btn-sm"
            onClick={() => endSession(type, session._id)}
          >
            End Session
          </button>
        )}

        <button
          className="btn btn-secondary btn-sm"
          onClick={() => {
            const url = `${window.location.origin}/${type}/${session._id}`;
            navigator.clipboard.writeText(url);
            alert('Session URL copied to clipboard!');
          }}
        >
          Copy Share URL
        </button>

        <button
          className="btn btn-danger btn-sm"
          onClick={() => deleteSession(type, session._id)}
          title={`Delete this ${type} permanently`}
        >
          🗑️ Delete
        </button>
      </div>
    </div>
  );

  if (loading) {
    return <div className="loading">Loading sessions...</div>;
  }

  return (
    <div className="control-center">
      <div className="dashboard-header">
        <h1 className="dashboard-title">Control Center</h1>
        <p className="dashboard-subtitle">
          Monitor and manage all your live polls and quizzes
        </p>
        <button 
          className="btn btn-primary"
          onClick={() => navigate('/admin')}
        >
          Create New Session
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="sessions-grid">
        <div className="sessions-section">
          <h2>📊 Live Polls ({polls.length})</h2>
          {polls.length === 0 ? (
            <div className="empty-state">
              <p>No polls created yet</p>
              <button 
                className="btn btn-primary"
                onClick={() => navigate('/admin')}
              >
                Create Your First Poll
              </button>
            </div>
          ) : (
            <div className="sessions-list">
              {polls.map(poll => (
                <SessionCard key={poll._id} session={poll} type="poll" />
              ))}
            </div>
          )}
        </div>

        <div className="sessions-section">
          <h2>🧠 Live Quizzes ({quizzes.length})</h2>
          {quizzes.length === 0 ? (
            <div className="empty-state">
              <p>No quizzes created yet</p>
              <button 
                className="btn btn-primary"
                onClick={() => navigate('/admin')}
              >
                Create Your First Quiz
              </button>
            </div>
          ) : (
            <div className="sessions-list">
              {quizzes.map(quiz => (
                <SessionCard key={quiz._id} session={quiz} type="quiz" />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Share URL Modal - QR Code removed */}
    </div>
  );
}

export default ControlCenter;
