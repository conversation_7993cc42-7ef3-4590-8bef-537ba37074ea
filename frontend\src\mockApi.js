// Mock API for development when backend is not available
let polls = [];
let quizzes = [];
let pollCounter = 1;
let quizCounter = 1;

// Generate session codes
function generateSessionCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Add some initial test data
polls.push({
  _id: 'poll_test1',
  sessionCode: 'TEST01',
  question: 'What is your favorite programming language?',
  options: [
    { text: 'JavaScript', voteCount: 5 },
    { text: 'Python', voteCount: 3 },
    { text: 'Java', voteCount: 2 },
    { text: 'C++', voteCount: 1 }
  ],
  isActive: true,
  showResults: true,
  totalVotes: 11,
  participants: [],
  createdAt: new Date()
});

quizzes.push({
  _id: 'quiz_test1',
  sessionCode: 'QUIZ01',
  question: 'What is the capital of France?',
  options: [
    { text: 'London', selectedCount: 1 },
    { text: 'Berlin', selectedCount: 0 },
    { text: 'Paris', selectedCount: 8 },
    { text: 'Madrid', selectedCount: 2 }
  ],
  correctAnswer: 2,
  isActive: true,
  showResults: true,
  totalAnswers: 11,
  participants: [],
  createdAt: new Date()
});

const mockApi = {
  // Polls
  createPoll: async (data) => {
    console.log('Mock API: Creating poll', data);
    const { question, options } = data;
    
    if (!question || !options || options.length < 2 || options.length > 5) {
      throw new Error('Question is required and must have 2-5 options');
    }

    const formattedOptions = options.map(option => ({
      text: option,
      voteCount: 0
    }));

    const sessionCode = generateSessionCode();
    const poll = {
      _id: `poll_${pollCounter++}`,
      sessionCode: sessionCode,
      question,
      options: formattedOptions,
      isActive: true,
      showResults: true,
      totalVotes: 0,
      participants: [],
      createdAt: new Date()
    };

    polls.push(poll);
    
    return {
      data: {
        ...poll,
        joinUrl: `http://localhost:3000/poll/${poll._id}`
      }
    };
  },

  getPolls: async () => {
    console.log('Mock API: Getting polls');
    return { data: polls };
  },

  getPoll: async (id) => {
    console.log('Mock API: Getting poll', id);
    const poll = polls.find(p => p._id === id);
    if (!poll) {
      throw new Error('Poll not found');
    }
    return { data: poll };
  },

  // Quizzes
  createQuiz: async (data) => {
    console.log('Mock API: Creating quiz', data);
    const { question, options, correctAnswer } = data;
    
    if (!question || !options || options.length < 2 || options.length > 5) {
      throw new Error('Question is required and must have 2-5 options');
    }

    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
      throw new Error('Valid correct answer index is required');
    }

    const formattedOptions = options.map(option => ({
      text: option,
      selectedCount: 0
    }));

    const sessionCode = generateSessionCode();
    const quiz = {
      _id: `quiz_${quizCounter++}`,
      sessionCode: sessionCode,
      question,
      options: formattedOptions,
      correctAnswer,
      isActive: true,
      showResults: true,
      totalAnswers: 0,
      participants: [],
      createdAt: new Date()
    };

    quizzes.push(quiz);
    
    return {
      data: {
        ...quiz,
        joinUrl: `http://localhost:3000/quiz/${quiz._id}`
      }
    };
  },

  getQuizzes: async () => {
    console.log('Mock API: Getting quizzes');
    return { data: quizzes };
  },

  getQuiz: async (id) => {
    console.log('Mock API: Getting quiz', id);
    const quiz = quizzes.find(q => q._id === id);
    if (!quiz) {
      throw new Error('Quiz not found');
    }
    return { data: quiz };
  },

  // Session lookup
  getSession: async (code) => {
    console.log('Mock API: Looking up session', code);
    const upperCode = code.toUpperCase();

    // Check polls first
    const poll = polls.find(p => p.sessionCode === upperCode);
    if (poll) {
      return { data: { ...poll, type: 'poll' } };
    }

    // Check quizzes
    const quiz = quizzes.find(q => q.sessionCode === upperCode);
    if (quiz) {
      return { data: { ...quiz, type: 'quiz' } };
    }

    throw new Error('Session not found');
  },

  // Voting/Answering
  vote: async (pollId, data) => {
    console.log('Mock API: Voting', pollId, data);
    const poll = polls.find(p => p._id === pollId);
    if (!poll) {
      throw new Error('Poll not found');
    }

    const { optionIndex } = data;
    if (optionIndex >= 0 && optionIndex < poll.options.length) {
      poll.options[optionIndex].voteCount += 1;
      poll.totalVotes += 1;
    }

    return { data: poll };
  },

  answer: async (quizId, data) => {
    console.log('Mock API: Answering', quizId, data);
    const quiz = quizzes.find(q => q._id === quizId);
    if (!quiz) {
      throw new Error('Quiz not found');
    }

    const { optionIndex } = data;
    const isCorrect = optionIndex === quiz.correctAnswer;
    
    if (optionIndex >= 0 && optionIndex < quiz.options.length) {
      quiz.options[optionIndex].selectedCount += 1;
      quiz.totalAnswers += 1;
    }

    return { 
      data: {
        quiz,
        isCorrect,
        correctAnswer: quiz.correctAnswer
      }
    };
  }
};

export default mockApi;
