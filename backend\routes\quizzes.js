const express = require('express');
// QRCode removed as requested
const Quiz = require('../models/Quiz');

const router = express.Router();

// Generate simple session codes
function generateSessionCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Create a new quiz
router.post('/', async (req, res) => {
  try {
    const { question, options, correctAnswer } = req.body;
    
    // Validation
    if (!question || !options || options.length < 2 || options.length > 5) {
      return res.status(400).json({ 
        error: 'Question is required and must have 2-5 options' 
      });
    }

    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
      return res.status(400).json({ 
        error: 'Valid correct answer index is required' 
      });
    }

    // Format options for database
    const formattedOptions = options.map(option => ({
      text: option,
      selectedCount: 0
    }));

    // Generate unique session code
    let sessionCode;
    let isUnique = false;
    while (!isUnique) {
      sessionCode = generateSessionCode();
      const existingQuiz = await Quiz.findOne({ sessionCode });
      if (!existingQuiz) {
        isUnique = true;
      }
    }

    const quiz = new Quiz({
      question,
      sessionCode,
      options: formattedOptions,
      correctAnswer,
      totalAnswers: 0
    });

    const savedQuiz = await quiz.save();

    // Return quiz data with join URL (QR code removed)
    const joinUrl = `${process.env.CLIENT_URL}/quiz/${savedQuiz._id}`;

    res.status(201).json({
      ...savedQuiz.toObject(),
      joinUrl: joinUrl
    });
  } catch (error) {
    console.error('Create quiz error:', error);
    res.status(500).json({ error: 'Failed to create quiz' });
  }
});

// Get all quizzes
router.get('/', async (req, res) => {
  try {
    const quizzes = await Quiz.find().sort({ createdAt: -1 });
    res.json(quizzes);
  } catch (error) {
    console.error('Get quizzes error:', error);
    res.status(500).json({ error: 'Failed to get quizzes' });
  }
});

// Get quiz by ID
router.get('/:id', async (req, res) => {
  try {
    const quiz = await Quiz.findById(req.params.id);
    
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    res.json(quiz);
  } catch (error) {
    console.error('Get quiz error:', error);
    res.status(500).json({ error: 'Failed to get quiz' });
  }
});

// Toggle result visibility
router.patch('/:id/toggle-results', async (req, res) => {
  try {
    const quiz = await Quiz.findById(req.params.id);
    
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    quiz.showResults = !quiz.showResults;
    await quiz.save();
    
    res.json(quiz);
  } catch (error) {
    console.error('Toggle results error:', error);
    res.status(500).json({ error: 'Failed to toggle results' });
  }
});

// End quiz
router.patch('/:id/end', async (req, res) => {
  try {
    const quiz = await Quiz.findById(req.params.id);
    
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    quiz.isActive = false;
    await quiz.save();
    
    res.json(quiz);
  } catch (error) {
    console.error('End quiz error:', error);
    res.status(500).json({ error: 'Failed to end quiz' });
  }
});

// Answer a quiz
router.post('/:id/answer', async (req, res) => {
  try {
    const { optionIndex, socketId } = req.body;
    const quiz = await Quiz.findById(req.params.id);

    if (!quiz || !quiz.isActive) {
      return res.status(404).json({ error: 'Quiz not found or inactive' });
    }

    // Check if user already answered
    const participant = quiz.participants.find(p => p.socketId === socketId);
    if (participant && participant.hasAnswered) {
      return res.status(400).json({ error: 'You have already answered' });
    }

    // Validate option index
    if (optionIndex < 0 || optionIndex >= quiz.options.length) {
      return res.status(400).json({ error: 'Invalid option' });
    }

    const isCorrect = optionIndex === quiz.correctAnswer;

    // Update answer count
    quiz.options[optionIndex].selectedCount += 1;
    quiz.totalAnswers += 1;

    // Add or update participant
    if (participant) {
      participant.hasAnswered = true;
      participant.selectedOption = optionIndex;
      participant.isCorrect = isCorrect;
    } else {
      quiz.participants.push({
        socketId,
        hasAnswered: true,
        selectedOption: optionIndex,
        isCorrect
      });
    }

    await quiz.save();
    
    res.json({
      quiz,
      isCorrect,
      correctAnswer: quiz.correctAnswer
    });
  } catch (error) {
    console.error('Answer error:', error);
    res.status(500).json({ error: 'Failed to process answer' });
  }
});

module.exports = router;
