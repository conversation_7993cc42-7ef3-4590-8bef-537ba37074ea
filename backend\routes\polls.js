const express = require('express');
// QRCode removed as requested
const Poll = require('../models/Poll');

const router = express.Router();

// Generate simple session codes
function generateSessionCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Create a new poll
router.post('/', async (req, res) => {
  try {
    const { question, options } = req.body;
    
    // Validation
    if (!question || !options || options.length < 2 || options.length > 5) {
      return res.status(400).json({ 
        error: 'Question is required and must have 2-5 options' 
      });
    }

    // Format options for database
    const formattedOptions = options.map(option => ({
      text: option,
      voteCount: 0
    }));

    // Generate unique session code
    let sessionCode;
    let isUnique = false;
    while (!isUnique) {
      sessionCode = generateSessionCode();
      const existingPoll = await Poll.findOne({ sessionCode });
      if (!existingPoll) {
        isUnique = true;
      }
    }

    const poll = new Poll({
      question,
      sessionCode,
      options: formattedOptions,
      totalVotes: 0
    });

    const savedPoll = await poll.save();

    // Return poll data with join URL (QR code removed)
    const joinUrl = `${process.env.CLIENT_URL}/poll/${savedPoll._id}`;

    res.status(201).json({
      ...savedPoll.toObject(),
      joinUrl: joinUrl
    });
  } catch (error) {
    console.error('Create poll error:', error);
    res.status(500).json({ error: 'Failed to create poll' });
  }
});

// Get all polls
router.get('/', async (req, res) => {
  try {
    const polls = await Poll.find().sort({ createdAt: -1 });
    res.json(polls);
  } catch (error) {
    console.error('Get polls error:', error);
    res.status(500).json({ error: 'Failed to get polls' });
  }
});

// Get poll by ID
router.get('/:id', async (req, res) => {
  try {
    const poll = await Poll.findById(req.params.id);
    
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    res.json(poll);
  } catch (error) {
    console.error('Get poll error:', error);
    res.status(500).json({ error: 'Failed to get poll' });
  }
});

// Toggle result visibility
router.patch('/:id/toggle-results', async (req, res) => {
  try {
    const poll = await Poll.findById(req.params.id);
    
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    poll.showResults = !poll.showResults;
    await poll.save();
    
    res.json(poll);
  } catch (error) {
    console.error('Toggle results error:', error);
    res.status(500).json({ error: 'Failed to toggle results' });
  }
});

// End poll
router.patch('/:id/end', async (req, res) => {
  try {
    const poll = await Poll.findById(req.params.id);
    
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    poll.isActive = false;
    await poll.save();
    
    res.json(poll);
  } catch (error) {
    console.error('End poll error:', error);
    res.status(500).json({ error: 'Failed to end poll' });
  }
});

// Vote on a poll
router.post('/:id/vote', async (req, res) => {
  try {
    const { optionIndex, socketId } = req.body;
    const poll = await Poll.findById(req.params.id);

    if (!poll || !poll.isActive) {
      return res.status(404).json({ error: 'Poll not found or inactive' });
    }

    // Check if user already voted
    const participant = poll.participants.find(p => p.socketId === socketId);
    if (participant && participant.hasVoted) {
      return res.status(400).json({ error: 'You have already voted' });
    }

    // Validate option index
    if (optionIndex < 0 || optionIndex >= poll.options.length) {
      return res.status(400).json({ error: 'Invalid option' });
    }

    // Update vote count
    poll.options[optionIndex].voteCount += 1;
    poll.totalVotes += 1;

    // Add or update participant
    if (participant) {
      participant.hasVoted = true;
      participant.selectedOption = optionIndex;
    } else {
      poll.participants.push({
        socketId,
        hasVoted: true,
        selectedOption: optionIndex
      });
    }

    await poll.save();
    res.json(poll);
  } catch (error) {
    console.error('Vote error:', error);
    res.status(500).json({ error: 'Failed to process vote' });
  }
});

module.exports = router;
